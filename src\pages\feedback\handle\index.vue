<script setup lang="ts">
import { useConfirm } from 'primevue'
import { useRoute, useRouter } from 'vue-router'
import { useFeedbackHandleForm } from './schema'
import { SolutionApi, UrlDecryptionApi } from '~/api/feedback/solution'
import type { FeedbackTriggerSearchParam, FeedbackTriggerSolution, SolutionRejectRequest } from '~/api/feedback/solution/type'
import { TriggerRecordApi } from '~/api/feedback/trigger'
import type { FeedbackTriggerRecord } from '~/api/feedback/trigger/type'
import { TriggerSendApi } from '~/api/feedback/trigger-send'
import type { FeedbackTriggerSend } from '~/api/feedback/trigger-send/type'
import { error, success } from '~/composables/toast'

// 接收路由参数作为 props
const props = defineProps<FeedbackTriggerSearchParam>()

// 路由和表单
const router = useRouter()
const route = useRoute()
const confirm = useConfirm()
const { handleSubmit, setValues } = useFeedbackHandleForm()

// 实际使用的参数（可能来自路由参数或解密后的参数）
const actualParams = ref<FeedbackTriggerSearchParam>({
  mode: props.mode,
  triggerRecordId: props.triggerRecordId,
  triggerSendId: props.triggerSendId,
  userId: props.userId,
  userName: props.userName,
})

// 响应式状态
const loading = ref(false)
const saving = ref(false)
const submitting = ref(false)
const closing = ref(false)
const rejecting = ref(false)
const showRejectDialog = ref(false)
const rejectReason = ref('')
const recordData = ref<FeedbackTriggerRecord | null>(null)
const solutionData = ref<FeedbackTriggerSolution | null>(null)
const sendData = ref<FeedbackTriggerSend | null>(null)

// 权限控制
const isViewMode = computed(() => actualParams.value.mode === 'view')
const isEditMode = computed(() => actualParams.value.mode === 'edit')
const isCloseMode = computed(() => actualParams.value.mode === 'close')

// URL参数解密
async function decryptUrlParams() {
  const dataParam = route.query.data as string
  if (!dataParam) {
    // 没有加密参数，使用路由参数
    return
  }

  try {
    const response = await UrlDecryptionApi.decrypt(dataParam)
    if (response.success && response.data) {
      // 更新实际参数
      actualParams.value = {
        mode: (response.data.mode as 'view' | 'edit' | 'close') || props.mode,
        triggerRecordId: response.data.triggerRecordId || props.triggerRecordId,
        triggerSendId: response.data.triggerSendId || props.triggerSendId,
        userId: response.data.userId,
        userName: response.data.userName,
      }
    }
    else {
      error(`URL参数解密失败: ${response.message}`)
    }
  }
  catch (err) {
    console.error('URL参数解密出错:', err)
    error('URL参数解密出错，将使用默认参数')
  }
}

// 加载记录数据
async function loadRecord() {
  if (!actualParams.value.triggerRecordId || !actualParams.value.triggerSendId) {
    error('缺少必要参数')
    router.back()
    return
  }

  try {
    loading.value = true

    const param = {
      mode: actualParams.value.mode,
      triggerRecordId: actualParams.value.triggerRecordId,
      triggerSendId: actualParams.value.triggerSendId,
    }
    // 并行加载触发记录、解决方案数据和发送记录数据
    const [triggerRecord, solution, sendRecord] = await Promise.all([
      TriggerRecordApi.get(actualParams.value.triggerRecordId),
      SolutionApi.getByTriggerRecordAndSend(param), // 解决方案可能不存在
      TriggerSendApi.findById(actualParams.value.triggerSendId), // 获取发送记录详情
    ])

    recordData.value = triggerRecord
    solutionData.value = solution
    sendData.value = sendRecord

    // 设置表单初始值
    setValues({
      solution: solution?.solution || '',
      solveTime: solution?.solveTime ? new Date(solution.solveTime) : new Date(),
    })
  }
  catch (err) {
    console.error(err)
  }
  finally {
    loading.value = false
  }
}

// 保存解决方案
const save = handleSubmit(async (values) => {
  if (!actualParams.value.triggerRecordId || !actualParams.value.triggerSendId) {
    error('缺少必要参数')
    return
  }

  // 权限检查
  if (actualParams.value.mode === 'view' || actualParams.value.mode === 'close') {
    error('当前为查看模式，无法保存')
    return
  }

  if (!values.solution.trim()) {
    error('请输入解决方案')
    return
  }

  try {
    saving.value = true
    const solutionParam = {
      solution: values.solution.trim(),
      solveTime: values.solveTime,
      triggerRecordId: actualParams.value.triggerRecordId,
      triggerSendId: actualParams.value.triggerSendId,
      solverId: actualParams.value.userId,
      solverName: actualParams.value.userName,
    }
    // 根据是否存在解决方案决定创建或更新
    if (solutionData.value?.id) {
      // 如果存在解决方案ID，则进行更新
      await SolutionApi.update(solutionData.value.id, solutionParam)
      success('解决方案更新成功')
    }
    else {
      // 如果不存在解决方案ID，则创建新的
      await SolutionApi.create(solutionParam)
      success('解决方案创建成功')
    }

    // 重新加载数据以显示最新的解决方案
    await loadRecord()
  }
  catch (err) {
    error('保存失败')
    console.error(err)
  }
  finally {
    saving.value = false
  }
})

// 提交解决方案
function submitSolution(event: Event) {
  // 权限检查
  if (actualParams.value.mode === 'view' || actualParams.value.mode === 'close') {
    error('当前为查看模式，无法提交')
    return
  }

  if (!solutionData.value?.id) {
    error('请先保存解决方案')
    return
  }

  if (!solutionData.value.solution?.trim()) {
    error('必须填写解决方案才能提交')
    return
  }
  // 使用PrimeVue确认对话框
  confirm.require({
    target: event.currentTarget as HTMLElement,
    group: 'confirm',
    message: '提交后将无法再修改解决方案，确定要提交吗？',
    header: '确认提交',
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: '取消',
      severity: 'secondary',
      outlined: true,
    },
    acceptProps: {
      label: '确定提交',
      severity: 'success',
    },
    accept: async () => {
      try {
        submitting.value = true
        const result = await SolutionApi.submit(solutionData.value!.id)
        solutionData.value = result
        success('解决方案提交成功')
      }
      catch (err) {
        error('提交失败')
        console.error(err)
      }
      finally {
        submitting.value = false
      }
    },
  })
}

// 关闭异常
function closeException(event: Event) {
  // 权限检查
  if (actualParams.value.mode !== 'close') {
    error('当前模式无法关闭异常')
    return
  }

  if (!actualParams.value.triggerRecordId) {
    error('缺少触发记录ID')
    return
  }

  if (recordData.value?.triggerClose) {
    error('异常已经关闭')
    return
  }

  // 使用PrimeVue确认对话框
  confirm.require({
    target: event.currentTarget as HTMLElement,
    group: 'confirm',
    message: '确定要关闭这个异常吗？关闭后异常状态将变为已关闭。',
    header: '确认关闭异常',
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: '取消',
      severity: 'secondary',
      outlined: true,
    },
    acceptProps: {
      label: '确定关闭',
      severity: 'danger',
    },
    accept: async () => {
      try {
        closing.value = true
        await TriggerRecordApi.closeException(actualParams.value.triggerRecordId)
        success('异常关闭成功')
        // 重新加载数据以显示最新状态
        await loadRecord()
      }
      catch (err) {
        error('关闭异常失败')
        console.error(err)
      }
      finally {
        closing.value = false
      }
    },
  })
}

// 退回解决方案
function rejectSolution(event: Event) {
  // 权限检查
  if (actualParams.value.mode === 'view' || actualParams.value.mode === 'edit') {
    error('当前模式无法退回解决方案')
    return
  }

  if (!solutionData.value?.id) {
    error('没有可退回的解决方案')
    return
  }

  if (solutionData.value.rejected) {
    error('解决方案已经被退回')
    return
  }

  if (!solutionData.value.submitted) {
    error('只能退回已提交的解决方案')
    return
  }

  // 使用PrimeVue确认对话框
  confirm.require({
    target: event.currentTarget as HTMLElement,
    group: 'confirm',
    message: '确定要退回这个解决方案吗？退回后解决方案状态将变为已退回。',
    header: '确认退回解决方案',
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: '取消',
      severity: 'secondary',
      outlined: true,
    },
    acceptProps: {
      label: '确定退回',
      severity: 'danger',
    },
    accept: () => {
      showRejectDialog.value = true
    },
  })
}

// 提交退回
async function submitReject() {
  if (!rejectReason.value.trim()) {
    error('请输入退回原因')
    return
  }

  if (!solutionData.value?.id) {
    error('没有可退回的解决方案')
    return
  }

  if (!actualParams.value.userId || !actualParams.value.userName) {
    error('缺少退回人信息')
    return
  }

  try {
    rejecting.value = true
    const rejectRequest: SolutionRejectRequest = {
      rejectReason: rejectReason.value.trim(),
      rejectorId: actualParams.value.userId,
      rejectorName: actualParams.value.userName,
    }

    const result = await SolutionApi.reject(solutionData.value.id, rejectRequest)
    solutionData.value = result
    success('解决方案退回成功')
    showRejectDialog.value = false
    rejectReason.value = ''

    // 重新加载数据以显示最新状态
    await loadRecord()
  }
  catch (err) {
    error('退回解决方案失败')
    console.error(err)
  }
  finally {
    rejecting.value = false
  }
}

// 取消退回
function cancelReject() {
  showRejectDialog.value = false
  rejectReason.value = ''
}

// 返回上一页
// function goBack() {
//   router.back()
// }

// 页面加载时获取数据
onMounted(async () => {
  // 首先尝试解密URL参数
  await decryptUrlParams()
  // 然后加载记录数据
  await loadRecord()
})
</script>

<template>
  <div class="min-h-screen p-4 md:p-6">
    <!-- 移动端友好的容器 -->
    <div class="mx-auto max-w-4xl">
      <!-- 头部 -->
      <div class="mb-6 flex items-center justify-between">
        <div class="flex items-center gap-3">
          <!-- <Button
            icon="pi pi-arrow-left"
            severity="secondary"
            outlined
            size="small"
            @click="goBack"
          /> -->
          <h1 class="text-xl text-gray-900 font-semibold md:text-2xl">
            {{ isEditMode ? '处理异常记录' : isCloseMode ? '关闭异常记录' : '查看异常记录' }}
          </h1>
          <div v-if="isViewMode" class="ml-2">
            <Tag value="仅查看" severity="info" />
          </div>
          <!-- <div v-if="isCloseMode" class="ml-2">
            <Tag value="关闭异常" severity="warning" />
          </div> -->
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center py-8">
        <ProgressSpinner />
      </div>

      <!-- 主要内容 -->
      <div v-else-if="recordData" class="space-y-6">
        <!-- 记录信息卡片 -->
        <Card class="shadow-sm">
          <template #title>
            <div class="flex items-center gap-2">
              <i class="pi pi-info-circle text-blue-500" />
              <span>异常信息</span>
            </div>
          </template>
          <template #content>
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
              <!-- <div class="space-y-1">
                <label class="text-sm text-gray-600 font-medium">异常编码</label>
                <p class="text-gray-900">
                  {{ recordData.anomaliesCode }}
                </p>
              </div> -->
              <div class="space-y-1">
                <label class="text-sm text-surface-600 font-medium dark:text-surface-400">异常名称</label>
                <p class="text-surface-900 dark:text-surface-100">
                  {{ formatDict(recordData.anomaliesCode, 'ABNORMAL_CATEGORY') }}
                </p>
              </div>
              <div class="space-y-1">
                <label class="text-sm text-surface-600 font-medium dark:text-surface-400">线体编码</label>
                <p class="text-surface-900 dark:text-surface-100">
                  {{ recordData.lineCode }}
                </p>
              </div>
              <div class="space-y-1">
                <label class="text-sm text-surface-600 font-medium dark:text-surface-400">触发人</label>
                <p class="text-surface-900 dark:text-surface-100">
                  {{ recordData.triggerUserName }}
                </p>
              </div>
              <div class="space-y-1">
                <label class="text-sm text-surface-600 font-medium dark:text-surface-400">触发时间</label>
                <p class="text-surface-900 dark:text-surface-100">
                  {{ new Date(recordData.triggerTime).toLocaleString() }}
                </p>
              </div>
              <div class="flex flex-col space-y-1">
                <label class="text-sm text-surface-600 font-medium dark:text-surface-400">状态</label>
                <div>
                  <Tag
                    :value="recordData.triggerClose ? '已关闭' : '进行中'"
                    :severity="recordData.triggerClose ? 'success' : 'warning'"
                  />
                </div>
              </div>
              <!-- <div class="space-y-1">
                <label class="text-sm text-gray-600 font-medium">触发记录ID</label>
                <p class="text-gray-900">
                  {{ props.triggerRecordId }}
                </p>
              </div>
              <div class="space-y-1">
                <label class="text-sm text-gray-600 font-medium">发送记录ID</label>
                <p class="text-gray-900">
                  {{ props.triggerSendId }}
                </p>
              </div> -->
            </div>
            <div v-if="recordData.anomaliesDetail" class="mt-4 space-y-1">
              <label class="text-sm text-surface-600 font-medium dark:text-surface-400">异常描述</label>
              <p class="text-surface-900 dark:text-surface-100">
                {{ recordData.anomaliesDetail }}
              </p>
            </div>

            <!-- 响应人和通知人信息 -->
            <div v-if="sendData" class="mt-6 space-y-4">
              <!-- 处理人 -->
              <div v-if="sendData.responders && sendData.responders.length > 0" class="space-y-2">
                <label class="text-sm text-surface-600 font-medium dark:text-surface-400">响应人</label>
                <div
                  class="max-h-24 flex flex-wrap gap-2 overflow-auto border border-surface-200 rounded-lg p-3 dark:border-surface-700"
                >
                  <Chip
                    v-for="responder in sendData.responders" :key="responder.respondentId"
                    :label="responder.respondentName || responder.respondentId"
                    class="border border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-700 dark:bg-blue-900/20 dark:text-blue-200"
                  />
                </div>
              </div>

              <!-- 抄送人 -->
              <div v-if="sendData.reporters && sendData.reporters.length > 0" class="space-y-2">
                <label class="text-sm text-surface-600 font-medium dark:text-surface-400">通知人</label>
                <div
                  class="max-h-24 flex flex-wrap gap-2 overflow-auto border border-surface-200 rounded-lg p-3 dark:border-surface-700"
                >
                  <Chip
                    v-for="reporter in sendData.reporters" :key="reporter.reporterId"
                    :label="reporter.reportName || reporter.reporterId"
                    class="border border-green-200 bg-green-50 text-green-800 dark:border-green-700 dark:bg-green-900/20 dark:text-green-200"
                  />
                </div>
              </div>
            </div>
          </template>
        </Card>

        <!-- 现有解决方案显示 -->
        <Card v-if="solutionData" class="shadow-sm">
          <template #title>
            <div class="flex items-center gap-2">
              <i class="pi pi-check-circle text-green-500" />
              <span>现有解决方案</span>
            </div>
          </template>
          <template #content>
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
              <div class="space-y-1">
                <label class="text-sm text-surface-600 font-medium dark:text-surface-400">解决人</label>
                <p class="text-surface-900 dark:text-surface-100">
                  {{ solutionData.solverName }}
                </p>
              </div>
              <div class="space-y-1">
                <label class="text-sm text-surface-600 font-medium dark:text-surface-400">解决时间</label>
                <p class="text-surface-900 dark:text-surface-100">
                  {{ new Date(solutionData.solveTime).toLocaleString() }}
                </p>
              </div>
              <div class="flex flex-col space-y-1">
                <label class="text-sm text-surface-600 font-medium dark:text-surface-400">提交状态</label>
                <div class="flex gap-2">
                  <Tag
                    :value="solutionData.submitted ? '已提交' : '未提交'"
                    :severity="solutionData.submitted ? 'success' : 'warning'"
                  />
                  <Tag
                    v-if="solutionData.rejected"
                    value="已退回"
                    severity="danger"
                  />
                </div>
              </div>
              <div v-if="solutionData.submitted && solutionData.submitTime" class="space-y-1">
                <label class="text-sm text-surface-600 font-medium dark:text-surface-400">提交时间</label>
                <p class="text-surface-900 dark:text-surface-100">
                  {{ new Date(solutionData.submitTime).toLocaleString() }}
                </p>
              </div>
              <div v-if="solutionData.rejected && solutionData.rejectorName" class="space-y-1">
                <label class="text-sm text-surface-600 font-medium dark:text-surface-400">退回人</label>
                <p class="text-surface-900 dark:text-surface-100">
                  {{ solutionData.rejectorName }}
                </p>
              </div>
              <div v-if="solutionData.rejected && solutionData.rejectTime" class="space-y-1">
                <label class="text-sm text-surface-600 font-medium dark:text-surface-400">退回时间</label>
                <p class="text-surface-900 dark:text-surface-100">
                  {{ new Date(solutionData.rejectTime).toLocaleString() }}
                </p>
              </div>
            </div>
            <div class="mt-4 space-y-1">
              <label class="text-sm text-surface-600 font-medium dark:text-surface-400">解决方案内容</label>
              <div class="rounded-lg bg-surface-50 p-4 dark:bg-surface-800">
                <p class="whitespace-pre-wrap text-surface-900 dark:text-surface-100">
                  {{ solutionData.solution }}
                </p>
              </div>
            </div>
            <div v-if="solutionData.rejected && solutionData.rejectReason" class="mt-4 space-y-1">
              <label class="text-sm text-surface-600 font-medium dark:text-surface-400">退回原因</label>
              <div class="border border-red-200 rounded-lg bg-red-50 p-4 dark:border-red-700 dark:bg-red-900/20">
                <p class="whitespace-pre-wrap text-red-800 dark:text-red-200">
                  {{ solutionData.rejectReason }}
                </p>
              </div>
            </div>
          </template>
        </Card>

        <!-- 解决方案表单 -->
        <Card class="shadow-sm">
          <template #title>
            <div class="flex items-center gap-2">
              <i class="pi pi-wrench text-green-500" />
              <span>
                {{ isViewMode || isCloseMode ? '解决方案详情' : (solutionData ? '更新解决方案' : '添加解决方案') }}
              </span>
              <Tag v-if="solutionData?.submitted" value="已提交" severity="success" />
              <Tag v-if="solutionData?.rejected" value="已退回" severity="danger" />
              <Tag v-if="isViewMode" value="仅查看" severity="info" />
              <!-- <Tag v-if="isCloseMode" value="关闭异常" severity="warning" /> -->
            </div>
          </template>
          <template #content>
            <!-- 查看模式提示 -->
            <div
              v-if="isViewMode"
              class="mb-4 border border-blue-200 rounded-lg bg-blue-50 p-4 dark:border-blue-700 dark:bg-blue-900/20"
            >
              <div class="flex items-center gap-2">
                <i class="pi pi-eye text-blue-600 dark:text-blue-400" />
                <span class="text-blue-800 font-medium dark:text-blue-200">当前为查看模式，无法编辑解决方案</span>
              </div>
            </div>

            <!-- 关闭异常模式提示 -->
            <div
              v-else-if="isCloseMode"
              class="mb-4 border border-orange-200 rounded-lg bg-orange-50 p-4 dark:border-orange-700 dark:bg-orange-900/20"
            >
              <div class="flex items-center gap-2">
                <i class="pi pi-times-circle text-orange-600 dark:text-orange-400" />
                <span class="text-orange-800 font-medium dark:text-orange-200">当前为关闭异常模式，可查看解决方案但无法编辑</span>
              </div>
            </div>

            <!-- 已退回提示 -->
            <div
              v-else-if="solutionData?.rejected"
              class="mb-4 border border-red-200 rounded-lg bg-red-50 p-4 dark:border-red-700 dark:bg-red-900/20"
            >
              <div class="flex items-center gap-2">
                <i class="pi pi-times-circle text-red-600 dark:text-red-400" />
                <span class="text-red-800 font-medium dark:text-red-200">解决方案已被退回，可以重新编辑和提交</span>
              </div>
            </div>

            <!-- 已提交提示 -->
            <div
              v-else-if="solutionData?.submitted"
              class="mb-4 border border-green-200 rounded-lg bg-green-50 p-4 dark:border-green-700 dark:bg-green-900/20"
            >
              <div class="flex items-center gap-2">
                <i class="pi pi-check-circle text-green-600 dark:text-green-400" />
                <span class="text-green-800 font-medium dark:text-green-200">解决方案已提交，无法再修改</span>
              </div>
            </div>

            <form @submit.prevent="save">
              <div class="space-y-4">
                <LTextarea
                  name="solution" label="解决方案" :textarea-props="{
                    rows: 6,
                    placeholder: '请详细描述解决方案...',
                    autoResize: true,
                    disabled: isViewMode || isCloseMode || (solutionData?.submitted && !solutionData?.rejected),
                  }"
                />
                <LDatePicker
                  name="solveTime" label="解决时间" :date-props="{
                    showTime: true,
                    hourFormat: '24',
                    showSeconds: true,
                    disabled: isViewMode || isCloseMode || (solutionData?.submitted && !solutionData?.rejected),
                  }"
                />
              </div>

              <!-- 操作按钮 -->
              <div v-if="isEditMode" class="mt-6 flex flex-col gap-3 sm:flex-row sm:justify-end">
                <!-- 保存按钮 -->
                <Button
                  v-if="!solutionData?.submitted || solutionData?.rejected" type="submit" label="保存解决方案" icon="pi pi-check"
                  :loading="saving" class="w-full sm:w-auto"
                />

                <!-- 提交按钮 -->
                <Button
                  v-if="solutionData?.id && (!solutionData?.submitted || solutionData?.rejected)" type="button" label="提交解决方案"
                  icon="pi pi-send" severity="success" :loading="submitting" class="w-full sm:w-auto"
                  @click="submitSolution($event)"
                />
              </div>

              <!-- 关闭异常按钮 -->
              <div v-if="isCloseMode" class="mt-6 flex flex-col gap-3 sm:flex-row sm:justify-end">
                <!-- 退回按钮 -->
                <Button
                  v-if="solutionData?.id && solutionData?.submitted && !solutionData?.rejected" type="button" label="退回解决方案"
                  icon="pi pi-times" severity="danger" class="w-full sm:w-auto"
                  @click="rejectSolution($event)"
                />
                <Button
                  v-if="!recordData?.triggerClose" type="button" label="关闭异常" icon="pi pi-times-circle"
                  severity="danger" :loading="closing" class="w-full sm:w-auto" @click="closeException($event)"
                />
                <div v-else class="flex items-center gap-2 text-green-600 dark:text-green-400">
                  <i class="pi pi-check-circle" />
                  <span class="font-medium">异常已关闭</span>
                </div>
              </div>
            </form>
          </template>
        </Card>
      </div>

      <!-- 错误状态 -->
      <div v-else class="flex flex-col items-center justify-center py-12">
        <i class="pi pi-exclamation-triangle mb-4 text-4xl text-red-500" />
        <div class="text-center">
          <p class="mb-2 text-surface-900 font-medium dark:text-surface-100">
            记录加载失败
          </p>
          <p class="mb-4 text-sm text-surface-600 dark:text-surface-400">
            请检查URL中的触发记录ID和发送记录ID是否正确
          </p>
          <div class="mb-6 text-xs text-surface-500 dark:text-surface-500">
            <p>触发记录ID: {{ actualParams.triggerRecordId }}</p>
            <p>发送记录ID: {{ actualParams.triggerSendId }}</p>
          </div>
        </div>
        <div class="flex gap-3">
          <Button label="重新加载" icon="pi pi-refresh" severity="secondary" outlined @click="loadRecord" />
          <!-- <Button
            label="返回上页"
            icon="pi pi-arrow-left"
            severity="secondary"
            @click="router.back()"
          /> -->
        </div>
      </div>
    </div>

    <!-- 退回解决方案对话框 -->
    <Dialog
      v-model:visible="showRejectDialog"
      modal
      header="退回解决方案"
      :style="{ width: '500px' }"
      class="p-fluid"
    >
      <div class="space-y-4">
        <div class="space-y-2">
          <label for="rejectReason" class="text-sm text-surface-600 font-medium dark:text-surface-400">退回原因 <span class="text-red-500">*</span></label>
          <Textarea
            id="rejectReason"
            v-model="rejectReason"
            rows="4"
            placeholder="请输入退回原因..."
            auto-resize
            class="w-full"
          />
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-2">
          <Button
            label="取消"
            icon="pi pi-times"
            severity="secondary"
            outlined
            @click="cancelReject"
          />
          <Button
            label="确定退回"
            icon="pi pi-check"
            severity="danger"
            :loading="rejecting"
            @click="submitReject"
          />
        </div>
      </template>
    </Dialog>
  </div>
</template>
